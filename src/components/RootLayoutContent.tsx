"use client";

import { usePathname } from "next/navigation";
import { LanguageSwitcher } from "./LanguageSwitcher";
import { useEffect, useState } from "react";

interface RootLayoutContentProps {
  children: React.ReactNode;
  locale: string;
  locales: readonly string[];
}

export default function RootLayoutContent({
  children,
  locale,
  locales,
}: RootLayoutContentProps) {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  // Ensure component is mounted before checking pathname
  useEffect(() => {
    setMounted(true);
  }, []);

  // Check if we're on an admin page
  const isAdminPage = pathname.includes("/admin");

  // Don't render LanguageSwitcher until mounted to avoid hydration mismatch
  const shouldShowLanguageSwitcher = mounted && !isAdminPage;

  return (
    <>
      {shouldShowLanguageSwitcher && (
        <LanguageSwitcher currentLocale={locale} locales={locales} />
      )}
      {children}
    </>
  );
}

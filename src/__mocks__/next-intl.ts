export const useTranslations = (namespace?: string) => {
  return (key: string, values?: Record<string, any>) => {
    const mockTranslations: Record<string, string> = {
      submit: "Login",
      signingIn: "Signing in...",
      loginTitle: "Admin Login",
      email: "Email",
      password: "Password",
      welcome: "Welcome! You are logged in as an admin.",
      dashboard: "Dashboard",
      adminManagement: "Admin Management",
      logout: "Logout",
      signingOut: "Signing out...",
      language: "Language",
      title: "Admin Management",
      addNew: "Add New Admin",
      id: "ID",
      createdAt: "Created At",
      actions: "Actions",
      edit: "Edit",
      delete: "Delete",
      noAdmins: "No admins found",
      confirmDelete:
        "Are you sure you want to delete admin '{email}'? This action cannot be undone.",
      createTitle: "Create New Admin",
      editTitle: "Edit Admin",
      confirmPassword: "Confirm Password",
      optional: "optional",
      create: "Create Admin",
      update: "Update Admin",
      cancel: "Cancel",
      creating: "Creating...",
      updating: "Updating...",
      emailRequired: "Email is required",
      passwordRequired: "Password is required",
      passwordMismatch: "Passwords do not match",
      passwordTooShort: "Password must be at least 8 characters long",
    };

    let result = mockTranslations[key] || key;

    if (values && key === "confirmDelete") {
      result = result.replace("{email}", values.email || "");
    }

    return result;
  };
};

export const useFormatter = () => ({
  dateTime: (date: Date) => date.toISOString(),
  number: (num: number) => num.toString(),
});

export const NextIntlClientProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => children;

export default {
  useTranslations,
  useFormatter,
  NextIntlClientProvider,
};

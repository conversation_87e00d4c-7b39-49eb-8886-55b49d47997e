import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import AdminsListPage from "@/app/[locale]/admin/admins/page";
import AdminForm from "@/app/[locale]/admin/_components/AdminForm";

// Mock dependencies
jest.mock("next-auth/react");
jest.mock("next/navigation");
jest.mock("next-intl", () => ({
  useTranslations: jest.fn(
    () => (key: string, values?: Record<string, any>) => {
      const mockTranslations: Record<string, string> = {
        title: "Admin Management",
        addNew: "Add New Admin",
        id: "ID",
        email: "Email",
        createdAt: "Created At",
        actions: "Actions",
        edit: "Edit",
        delete: "Delete",
        noAdmins: "No admins found",
        confirmDelete:
          "Are you sure you want to delete admin '{email}'? This action cannot be undone.",
        createTitle: "Create New Admin",
        editTitle: "Edit Admin",
        password: "Password",
        confirmPassword: "Confirm Password",
        optional: "optional",
        create: "Create Admin",
        update: "Update Admin",
        cancel: "Cancel",
        creating: "Creating...",
        updating: "Updating...",
        emailRequired: "Email is required",
        passwordRequired: "Password is required",
        passwordMismatch: "Passwords do not match",
        passwordTooShort: "Password must be at least 8 characters long",
        // AdminSidebar namespace
        dashboard: "Dashboard",
        adminManagement: "Admin Management",
        logout: "Logout",
        signingOut: "Signing out...",
      };

      let result = mockTranslations[key] || key;

      // Handle interpolation for confirmDelete
      if (values && key === "confirmDelete") {
        result = result.replace("{email}", values.email || "");
      }

      return result;
    }
  ),
}));

const mockUseSession = useSession as jest.MockedFunction<typeof useSession>;
const mockUseRouter = useRouter as jest.MockedFunction<typeof useRouter>;
const mockUseParams = useParams as jest.MockedFunction<typeof useParams>;

const mockPush = jest.fn();
const mockRouter = {
  push: mockPush,
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  replace: jest.fn(),
  prefetch: jest.fn().mockResolvedValue(undefined),
};

const mockSession = {
  user: { id: "admin-1", email: "<EMAIL>" },
  expires: "9999-12-31T23:59:59.999Z",
};

const mockAdmins = [
  {
    id: "admin-1",
    email: "<EMAIL>",
    createdAt: "2024-01-01T00:00:00.000Z",
    updatedAt: "2024-01-01T00:00:00.000Z",
  },
  {
    id: "admin-2",
    email: "<EMAIL>",
    createdAt: "2024-01-02T00:00:00.000Z",
    updatedAt: "2024-01-02T00:00:00.000Z",
  },
];

// Mock fetch globally
global.fetch = jest.fn();

describe("Admin Management Simple Tests", () => {
  beforeEach(() => {
    jest.clearAllMocks();

    mockUseSession.mockReturnValue({
      data: mockSession,
      status: "authenticated",
      update: jest.fn(),
    });

    mockUseRouter.mockReturnValue(mockRouter);
    mockUseParams.mockReturnValue({ locale: "en" });

    // Mock successful fetch responses by default
    (global.fetch as jest.Mock).mockResolvedValue({
      ok: true,
      json: async () => mockAdmins,
    });
  });

  describe("Admin List Page", () => {
    it("should render admin list with correct data", async () => {
      render(<AdminsListPage />);

      await waitFor(() => {
        expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
        expect(screen.getByText("<EMAIL>")).toBeInTheDocument();
      });

      // Check if table headers are present
      expect(screen.getByText("ID")).toBeInTheDocument();
      expect(screen.getByText("Email")).toBeInTheDocument();
      expect(screen.getByText("Created At")).toBeInTheDocument();
      expect(screen.getByText("Actions")).toBeInTheDocument();
    });

    it("should handle API errors gracefully", async () => {
      (global.fetch as jest.Mock).mockRejectedValueOnce(
        new Error("Network error")
      );

      render(<AdminsListPage />);

      await waitFor(() => {
        expect(screen.getByText("Failed to load admins")).toBeInTheDocument();
      });
    });
  });

  describe("Admin Form Component", () => {
    it("should render create form correctly", () => {
      render(<AdminForm mode="create" />);

      expect(screen.getByText("Create New Admin")).toBeInTheDocument();
      expect(screen.getByLabelText("Email")).toBeInTheDocument();
      expect(screen.getByLabelText("Password")).toBeInTheDocument();
      expect(screen.getByLabelText("Confirm Password")).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: /create admin/i })
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: /cancel/i })
      ).toBeInTheDocument();
    });

    it("should render edit form correctly", () => {
      const initialData = {
        id: "bd6e4093-e65b-42a4-b6f6-00b30ef48e20",
        email: "<EMAIL>",
      };

      render(<AdminForm mode="edit" initialData={initialData} />);

      expect(screen.getByText("Edit Admin")).toBeInTheDocument();
      expect(
        screen.getByDisplayValue("<EMAIL>")
      ).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: /update admin/i })
      ).toBeInTheDocument();
    });

    it("should show validation errors", async () => {
      const user = userEvent.setup();

      render(<AdminForm mode="create" />);

      // Remove required attributes to test our custom validation
      const emailInput = screen.getByLabelText(/email/i);
      const passwordInput = screen.getByLabelText(/^password/i);
      const confirmPasswordInput = screen.getByLabelText(/confirm password/i);

      emailInput.removeAttribute("required");
      passwordInput.removeAttribute("required");
      confirmPasswordInput.removeAttribute("required");

      await user.click(screen.getByRole("button", { name: /create admin/i }));

      await waitFor(() => {
        expect(screen.getByRole("alert")).toHaveTextContent(
          "Email is required"
        );
      });
    });

    it("should handle form submission", async () => {
      const user = userEvent.setup();

      (global.fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({
          id: "new-admin",
          email: "<EMAIL>",
          createdAt: "2024-01-03T00:00:00.000Z",
          updatedAt: "2024-01-03T00:00:00.000Z",
        }),
      });

      render(<AdminForm mode="create" />);

      // Fill in the form
      await user.type(screen.getByLabelText("Email"), "<EMAIL>");
      await user.type(screen.getByLabelText("Password"), "password123");
      await user.type(screen.getByLabelText("Confirm Password"), "password123");

      // Submit the form
      await user.click(screen.getByRole("button", { name: /create admin/i }));

      await waitFor(() => {
        expect(global.fetch).toHaveBeenCalledWith("/api/admin/admins", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email: "<EMAIL>",
            password: "password123",
          }),
        });
      });

      expect(mockPush).toHaveBeenCalledWith("/en/admin/admins");
    });
  });

  describe("Authentication", () => {
    it("should redirect to login when not authenticated", () => {
      mockUseSession.mockReturnValue({
        data: null,
        status: "unauthenticated",
        update: jest.fn(),
      });

      render(<AdminsListPage />);

      expect(mockPush).toHaveBeenCalledWith("/en/admin/login");
    });
  });
});

import { getServerSession } from "next-auth";
import { GET as authOptions } from "@/app/api/auth/[...nextauth]/route";
import { redirect } from "next/navigation";
import { getTranslations } from "next-intl/server";
import { Session } from "next-auth";

export default async function AdminDashboard({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;

  let session: Session | null = null;
  try {
    session = await getServerSession(authOptions);
  } catch (error) {
    console.log("JWT session error, redirecting to login:", error);
    redirect(`/${locale}/admin/login`);
  }

  if (!session?.user) {
    redirect(`/${locale}/admin/login`);
  }

  const t = await getTranslations({ locale, namespace: "AdminDashboard" });

  return (
    <>
      <header className="bg-gray-900 border-b border-gray-800 shadow p-4">
        <h2 className="text-xl font-semibold text-white">Dashboard</h2>
      </header>
      <main className="p-8">
        <h1 className="text-3xl font-bold mb-4 text-white">{t("title")}</h1>
        <p className="text-gray-300">{t("welcome")}</p>
      </main>
    </>
  );
}

"use client";

import { ReactNode, useEffect, useState } from "react";
import { usePathname } from "next/navigation";
import AdminSidebar from "./_components/AdminSidebar";

export default function AdminLayout({ children }: { children: ReactNode }) {
  const pathname = usePathname();
  const [mounted, setMounted] = useState(false);

  // Ensure component is mounted before checking pathname
  useEffect(() => {
    setMounted(true);
  }, []);

  // Don't show sidebar on login page, but only after mounting to avoid hydration issues
  const isLoginPage = mounted && pathname.includes("/login");

  // Always render the same structure on server and initial client render
  if (!mounted) {
    return (
      <div className="min-h-screen bg-black text-white flex">
        <AdminSidebar />
        <div className="flex-1">{children}</div>
      </div>
    );
  }

  if (isLoginPage) {
    return <>{children}</>;
  }

  return (
    <div className="min-h-screen bg-black text-white flex">
      <AdminSidebar />
      <div className="flex-1">{children}</div>
    </div>
  );
}

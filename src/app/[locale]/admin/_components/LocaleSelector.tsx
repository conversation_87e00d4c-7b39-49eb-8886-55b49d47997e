"use client";

import { usePathname, useParams } from "next/navigation";
import Link from "next/link";
import { useTranslations } from "next-intl";
import { Icon } from "@iconify/react";
import { useState, useRef, useEffect } from "react";

interface LocaleSelectorProps {
  locales: readonly string[];
}

export default function LocaleSelector({ locales }: LocaleSelectorProps) {
  const pathname = usePathname();
  const { locale: currentLocale } = useParams();
  const t = useTranslations("AdminSidebar");
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Remove the current locale from the pathname to get the base path
  const strippedPath = pathname.replace(/^\/(en|jp)/, "");

  // Close dropdown when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const getLocaleDisplayName = (locale: string) => {
    switch (locale) {
      case "en":
        return "English";
      case "jp":
        return "日本語";
      default:
        return locale.toUpperCase();
    }
  };

  const getLocaleFlag = (locale: string) => {
    switch (locale) {
      case "en":
        return "🇺🇸";
      case "jp":
        return "🇯🇵";
      default:
        return "🌐";
    }
  };

  const currentLocaleDisplay = getLocaleDisplayName(currentLocale as string);
  const currentLocaleFlag = getLocaleFlag(currentLocale as string);

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-3 w-full px-4 py-3 rounded-lg transition-all duration-200 text-gray-300 hover:bg-gray-800 hover:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50"
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <Icon icon="lucide:globe" className="h-5 w-5 flex-shrink-0" />
        <div className="flex items-center gap-2 flex-1 min-w-0">
          <span className="text-lg flex-shrink-0">{currentLocaleFlag}</span>
          <span className="text-sm font-medium truncate">
            {currentLocaleDisplay}
          </span>
        </div>
        <Icon
          icon={isOpen ? "lucide:chevron-up" : "lucide:chevron-down"}
          className={`h-4 w-4 transition-transform duration-200 flex-shrink-0 ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>

      {isOpen && (
        <div className="absolute bottom-full left-0 right-0 mb-2 bg-gray-800 border border-gray-700 rounded-lg shadow-xl overflow-hidden z-50 animate-in slide-in-from-bottom-2 duration-200">
          {locales.map((locale) => {
            if (locale === currentLocale) return null;

            return (
              <Link
                key={locale}
                href={`/${locale}${strippedPath}`}
                onClick={() => setIsOpen(false)}
                className="flex items-center gap-3 px-4 py-3 transition-all duration-200 text-gray-300 hover:bg-gray-700 hover:text-white focus:outline-none focus:bg-gray-700 focus:text-white"
              >
                <Icon icon="lucide:globe" className="h-5 w-5 flex-shrink-0" />
                <div className="flex items-center gap-2 min-w-0">
                  <span className="text-lg flex-shrink-0">
                    {getLocaleFlag(locale)}
                  </span>
                  <span className="text-sm font-medium truncate">
                    {getLocaleDisplayName(locale)}
                  </span>
                </div>
              </Link>
            );
          })}
        </div>
      )}
    </div>
  );
}

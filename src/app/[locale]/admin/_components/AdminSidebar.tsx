"use client";

import Link from "next/link";
import { useParams, usePathname } from "next/navigation";
import { useTranslations } from "next-intl";
import { useSession } from "next-auth/react";
import { Icon } from "@iconify/react";
import { routing } from "@/i18n/routing";
import AuthButton from "./AuthButton";
import LocaleSelector from "./LocaleSelector";

export default function AdminSidebar() {
  const { locale } = useParams();
  const pathname = usePathname();
  const { data: session } = useSession();
  const t = useTranslations("AdminSidebar");

  const menuItems = [
    {
      href: `/${locale}/admin`,
      label: t("dashboard"),
      icon: "lucide:layout-dashboard",
      exact: true,
    },
    {
      href: `/${locale}/admin/admins`,
      label: t("adminManagement"),
      icon: "lucide:users",
      exact: false,
    },
  ];

  const isActive = (href: string, exact: boolean) => {
    if (exact) {
      return pathname === href;
    }
    return pathname.startsWith(href);
  };

  return (
    <aside className="w-64 bg-gray-900 border-r border-gray-800 min-h-screen flex flex-col">
      {/* Header */}
      <div className="p-6 border-b border-gray-800">
        <h2 className="text-xl font-semibold text-white">Admin Panel</h2>
      </div>

      {/* Navigation */}
      <div className="p-6 flex-1">
        <nav className="space-y-2">
          {menuItems.map((item) => (
            <Link
              key={item.href}
              href={item.href}
              className={`flex items-center gap-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                isActive(item.href, item.exact)
                  ? "bg-blue-600 text-white shadow-lg"
                  : "text-gray-300 hover:bg-gray-800 hover:text-white"
              }`}
            >
              <Icon icon={item.icon} className="h-5 w-5" />
              <span className="font-medium">{item.label}</span>
            </Link>
          ))}
        </nav>
      </div>

      {/* Language selector section */}
      <div className="px-6 pb-4 border-t border-gray-800 pt-4">
        <div className="text-xs text-gray-400 uppercase tracking-wider mb-3 px-4">
          Language
        </div>
        <LocaleSelector locales={routing.locales} />
      </div>

      {/* User info and logout section */}
      <div className="p-6 border-t border-gray-800 bg-gray-950">
        <div className="mb-4">
          <div className="text-xs text-gray-400 uppercase tracking-wider mb-2">
            Logged in as
          </div>
          <div className="text-sm text-white font-medium truncate bg-gray-800 px-3 py-2 rounded-lg">
            {session?.user?.email}
          </div>
        </div>
        <AuthButton mode="logout" />
      </div>
    </aside>
  );
}

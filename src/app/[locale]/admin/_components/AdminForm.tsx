"use client";

import { useState } from "react";
import { useRouter, useParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Icon } from "@iconify/react";

interface AdminFormProps {
  mode: "create" | "edit";
  initialData?: {
    id: string;
    email: string;
  };
}

export default function AdminForm({ mode, initialData }: AdminFormProps) {
  const router = useRouter();
  const { locale } = useParams();
  const t = useTranslations("AdminForm");

  const [email, setEmail] = useState(initialData?.email || "");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");

    // Validation
    if (!email) {
      setError(t("emailRequired"));
      return;
    }

    if (mode === "create" && !password) {
      setError(t("passwordRequired"));
      return;
    }

    if (password && password !== confirmPassword) {
      setError(t("passwordMismatch"));
      return;
    }

    if (password && password.length < 8) {
      setError(t("passwordTooShort"));
      return;
    }

    try {
      setLoading(true);

      const requestData: any = { email };
      if (password) {
        requestData.password = password;
      }

      const url =
        mode === "create"
          ? "/api/admin/admins"
          : `/api/admin/admins/${initialData?.id}`;

      const method = mode === "create" ? "POST" : "PUT";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `Failed to ${mode} admin`);
      }

      // Redirect to admin list
      router.push(`/${locale}/admin/admins`);
    } catch (error: any) {
      console.error(`Error ${mode}ing admin:`, error);
      setError(error.message || `Failed to ${mode} admin`);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push(`/${locale}/admin/admins`);
  };

  return (
    <>
      <header className="bg-gray-900 border-b border-gray-800 shadow p-4">
        <h2 className="text-xl font-semibold text-white">
          {mode === "create" ? t("createTitle") : t("editTitle")}
        </h2>
      </header>

      <main className="p-8">
        <div className="max-w-md mx-auto bg-gray-900 rounded-lg border border-gray-800 p-6">
          {error && (
            <div
              role="alert"
              className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded text-red-300 text-sm"
            >
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label
                htmlFor="email"
                className="block text-white mb-2 text-sm font-medium"
              >
                {t("email")}
              </label>
              <Input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                disabled={loading}
                className="bg-gray-800 border-gray-600 text-white focus:border-blue-500"
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-white mb-2 text-sm font-medium"
              >
                {t("password")}{" "}
                {mode === "edit" && (
                  <span className="text-gray-400">({t("optional")})</span>
                )}
              </label>
              <Input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                disabled={loading}
                className="bg-gray-800 border-gray-600 text-white focus:border-blue-500"
                placeholder="••••••••"
                required={mode === "create"}
              />
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-white mb-2 text-sm font-medium"
              >
                {t("confirmPassword")}
              </label>
              <Input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                disabled={loading}
                className="bg-gray-800 border-gray-600 text-white focus:border-blue-500"
                placeholder="••••••••"
                required={mode === "create" || !!password}
              />
            </div>

            <div className="flex gap-3 pt-4">
              <Button type="submit" disabled={loading} className="flex-1 gap-2">
                {loading ? (
                  <>
                    <Icon
                      icon="lucide:loader-2"
                      className="h-4 w-4 animate-spin"
                    />
                    {mode === "create" ? t("creating") : t("updating")}
                  </>
                ) : (
                  <>
                    <Icon
                      icon={mode === "create" ? "lucide:plus" : "lucide:save"}
                      className="h-4 w-4"
                    />
                    {mode === "create" ? t("create") : t("update")}
                  </>
                )}
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={handleCancel}
                disabled={loading}
                className="gap-2"
              >
                <Icon icon="lucide:x" className="h-4 w-4" />
                {t("cancel")}
              </Button>
            </div>
          </form>
        </div>
      </main>
    </>
  );
}

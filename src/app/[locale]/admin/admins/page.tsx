"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { useTranslations } from "next-intl";
import { Button } from "@/components/ui/button";
import { Icon } from "@iconify/react";
import Link from "next/link";

interface Admin {
  id: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

export default function AdminsListPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { locale } = useParams();
  const t = useTranslations("AdminManagement");

  const [admins, setAdmins] = useState<Admin[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");
  const [deletingId, setDeletingId] = useState<string | null>(null);

  useEffect(() => {
    if (status === "loading") return;

    if (!session) {
      router.push(`/${locale}/admin/login`);
      return;
    }

    fetchAdmins();
  }, [session, status, router, locale]);

  const fetchAdmins = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/admin/admins");

      if (!response.ok) {
        throw new Error("Failed to fetch admins");
      }

      const data = await response.json();
      setAdmins(data);
    } catch (error) {
      console.error("Error fetching admins:", error);
      setError("Failed to load admins");
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string, email: string) => {
    if (!confirm(t("confirmDelete", { email }))) {
      return;
    }

    try {
      setDeletingId(id);
      const response = await fetch(`/api/admin/admins/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to delete admin");
      }

      await fetchAdmins();
    } catch (error: any) {
      console.error("Error deleting admin:", error);
      alert(error.message || "Failed to delete admin");
    } finally {
      setDeletingId(null);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(locale as string, {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  if (status === "loading" || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icon
          icon="lucide:loader-2"
          className="h-8 w-8 animate-spin text-white"
        />
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="text-red-400 text-center">{error}</div>
      </div>
    );
  }

  return (
    <>
      <header className="bg-gray-900 border-b border-gray-800 shadow p-4 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-white">{t("title")}</h2>
        <Link href={`/${locale}/admin/admins/new`}>
          <Button className="gap-2">
            <Icon icon="lucide:plus" className="h-4 w-4" />
            {t("addNew")}
          </Button>
        </Link>
      </header>

      <main className="p-8">
        <div className="bg-gray-900 rounded-lg border border-gray-800 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-800">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    {t("id")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    {t("email")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    {t("createdAt")}
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                    {t("actions")}
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-800">
                {admins.map((admin) => (
                  <tr key={admin.id} className="hover:bg-gray-800">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300 font-mono">
                      {admin.id.substring(0, 8)}...
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-white">
                      {admin.email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                      {formatDate(admin.createdAt)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                      <Link href={`/${locale}/admin/admins/${admin.id}/edit`}>
                        <Button size="sm" variant="outline" className="gap-1">
                          <Icon icon="lucide:edit" className="h-3 w-3" />
                          {t("edit")}
                        </Button>
                      </Link>
                      <Button
                        size="sm"
                        variant="destructive"
                        className="gap-1"
                        onClick={() => handleDelete(admin.id, admin.email)}
                        disabled={
                          deletingId === admin.id ||
                          session?.user?.id === admin.id
                        }
                      >
                        {deletingId === admin.id ? (
                          <Icon
                            icon="lucide:loader-2"
                            className="h-3 w-3 animate-spin"
                          />
                        ) : (
                          <Icon icon="lucide:trash-2" className="h-3 w-3" />
                        )}
                        {t("delete")}
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>

            {admins.length === 0 && (
              <div className="text-center py-12 text-gray-400">
                {t("noAdmins")}
              </div>
            )}
          </div>
        </div>
      </main>
    </>
  );
}

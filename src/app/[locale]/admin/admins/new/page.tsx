"use client";

import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { useEffect } from "react";
import { Icon } from "@iconify/react";
import AdminForm from "../../_components/AdminForm";

export default function NewAdminPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { locale } = useParams();

  useEffect(() => {
    if (status === "loading") return;
    
    if (!session) {
      router.push(`/${locale}/admin/login`);
    }
  }, [session, status, router, locale]);

  if (status === "loading") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icon icon="lucide:loader-2" className="h-8 w-8 animate-spin text-white" />
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return <AdminForm mode="create" />;
}

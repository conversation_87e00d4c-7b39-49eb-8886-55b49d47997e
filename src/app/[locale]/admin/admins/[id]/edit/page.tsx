"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { Icon } from "@iconify/react";
import AdminForm from "../../../_components/AdminForm";

interface Admin {
  id: string;
  email: string;
  createdAt: string;
  updatedAt: string;
}

export default function EditAdminPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const { locale, id } = useParams();
  
  const [admin, setAdmin] = useState<Admin | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    if (status === "loading") return;
    
    if (!session) {
      router.push(`/${locale}/admin/login`);
      return;
    }

    fetchAdmin();
  }, [session, status, router, locale, id]);

  const fetchAdmin = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/admin/admins/${id}`);
      
      if (!response.ok) {
        if (response.status === 404) {
          setError("Admin not found");
        } else {
          throw new Error("Failed to fetch admin");
        }
        return;
      }
      
      const data = await response.json();
      setAdmin(data);
    } catch (error) {
      console.error("Error fetching admin:", error);
      setError("Failed to load admin");
    } finally {
      setLoading(false);
    }
  };

  if (status === "loading" || loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Icon icon="lucide:loader-2" className="h-8 w-8 animate-spin text-white" />
      </div>
    );
  }

  if (!session) {
    return null;
  }

  if (error) {
    return (
      <div className="p-8">
        <div className="text-red-400 text-center">{error}</div>
      </div>
    );
  }

  if (!admin) {
    return (
      <div className="p-8">
        <div className="text-gray-400 text-center">Admin not found</div>
      </div>
    );
  }

  return (
    <AdminForm 
      mode="edit" 
      initialData={{
        id: admin.id,
        email: admin.email,
      }}
    />
  );
}

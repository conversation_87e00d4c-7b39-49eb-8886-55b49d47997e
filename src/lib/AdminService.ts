import { prisma } from "./Prisma";
import { hash } from "bcryptjs";

export async function getAdminByEmail(email: string) {
  return prisma.admin.findUnique({
    where: {
      email,
      deletedAt: null,
    },
  });
}

export async function getAdminById(id: string) {
  return prisma.admin.findUnique({
    where: {
      id,
      deletedAt: null,
    },
  });
}

export async function getAllAdmins() {
  return prisma.admin.findMany({
    where: {
      deletedAt: null,
    },
    select: {
      id: true,
      email: true,
      createdAt: true,
      updatedAt: true,
    },
    orderBy: {
      createdAt: "desc",
    },
  });
}

export async function createAdmin(email: string, password: string) {
  const existingAdmin = await prisma.admin.findUnique({
    where: { email },
  });

  if (existingAdmin) {
    throw new Error("Email already exists");
  }

  const hashedPassword = await hash(password, 10);

  return prisma.admin.create({
    data: {
      email,
      password: hashedPassword,
    },
    select: {
      id: true,
      email: true,
      createdAt: true,
      updatedAt: true,
    },
  });
}

export async function updateAdmin(
  id: string,
  data: { email?: string; password?: string }
) {
  // If email is being updated, check for uniqueness
  if (data.email) {
    const existingAdmin = await prisma.admin.findFirst({
      where: {
        email: data.email,
        id: { not: id },
      },
    });

    if (existingAdmin) {
      throw new Error("Email already exists");
    }
  }

  const updateData: any = {};

  if (data.email) {
    updateData.email = data.email;
  }

  if (data.password) {
    updateData.password = await hash(data.password, 10);
  }

  return prisma.admin.update({
    where: {
      id,
      deletedAt: null,
    },
    data: updateData,
    select: {
      id: true,
      email: true,
      createdAt: true,
      updatedAt: true,
    },
  });
}

export async function softDeleteAdmin(id: string) {
  return prisma.admin.update({
    where: {
      id,
      deletedAt: null,
    },
    data: {
      deletedAt: new Date(),
    },
  });
}
